import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Button,
  Searchbar,
  Chip,
  Menu,
  Divider,
  FAB,
  Portal,
  Dialog,
  Text as PaperText,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../../constants';
import { useRoomStore } from '../../store/roomStore';
import { RoomCard } from '../../components/cards/RoomCard';
import { formatPrice } from '../../utils/currency';
import { formatRoomType, formatRoomStatus, safeIncludes } from '../../utils/stringUtils';
import { Room, RoomType, RoomStatus } from '../../types/database';

export const AdminRoomsScreen = ({ navigation }: any) => {
  const { 
    rooms, 
    loading, 
    fetchRooms, 
    updateRoom, 
    deleteRoom 
  } = useRoomStore();
  
  const [refreshing, setRefreshing] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedType, setSelectedType] = React.useState<RoomType | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = React.useState<RoomStatus | 'all' | 'occupied'>('all');
  const [sortMenuVisible, setSortMenuVisible] = React.useState(false);
  const [sortBy, setSortBy] = React.useState<'number' | 'type' | 'price'>('number');
  const [deleteDialogVisible, setDeleteDialogVisible] = React.useState(false);
  const [roomToDelete, setRoomToDelete] = React.useState<Room | null>(null);

  React.useEffect(() => {
    loadRooms();
  }, []);

  // Add focus listener to refresh rooms when returning from add/edit screen
  React.useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadRooms();
    });

    return unsubscribe;
  }, [navigation]);

  const loadRooms = async () => {
    try {
      await fetchRooms();
    } catch (error) {
      console.error('Error loading rooms:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadRooms();
    setRefreshing(false);
  };

  const filteredAndSortedRooms = rooms
    .filter(room => {
      // Search logic: safeIncludes now handles empty search queries correctly
      const matchesSearch = safeIncludes(room.room_number, searchQuery) ||
                           safeIncludes(room.type, searchQuery);
      const matchesType = selectedType === 'all' || room.type === selectedType;
      const matchesStatus =
        selectedStatus === 'all' ||
        (selectedStatus === 'available' && room.is_available && room.status === 'available') ||
        (selectedStatus === 'booked' && room.status === 'booked') ||
        (selectedStatus === 'maintenance' && room.status === 'maintenance') ||
        (selectedStatus === 'cleaning' && room.status === 'cleaning') ||
        // Special case for occupied (not a status but a condition)
        (selectedStatus as string === 'occupied' && !room.is_available);



      return matchesSearch && matchesType && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'number':
          return a.room_number.localeCompare(b.room_number);
        case 'type':
          return (a.type || a.room_type).localeCompare(b.type || b.room_type);
        case 'price':
          return a.price_per_night - b.price_per_night;
        default:
          return 0;
      }
    });



  const handleRoomPress = (room: Room) => {
    navigation.navigate('AddEditRoom', { room });
  };

  const handleStatusChange = async (room: Room, newStatus: RoomStatus) => {
    try {
      await updateRoom(room.id, { 
        status: newStatus,
        is_available: newStatus === 'available'
      });
      Alert.alert('Success', 'Room status updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update room status');
    }
  };

  const confirmDeleteRoom = (room: Room) => {
    setRoomToDelete(room);
    setDeleteDialogVisible(true);
  };

  const handleDeleteRoom = async () => {
    if (!roomToDelete) return;
    
    try {
      await deleteRoom(roomToDelete.id);
      setDeleteDialogVisible(false);
      setRoomToDelete(null);
      Alert.alert('Success', 'Room deleted successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to delete room');
    }
  };

  const roomTypes: (RoomType | 'all')[] = ['all', 'standard', 'deluxe', 'suite', 'presidential'];
  // Added 'occupied' as a special status though it's not in the RoomStatus type
  const roomStatuses: (RoomStatus | 'all' | 'occupied')[] = ['all', 'available', 'booked', 'maintenance', 'cleaning', 'occupied'];

  const getStatusColor = (status: RoomStatus, isAvailable: boolean) => {
    if (!isAvailable) return colors.error;
    switch (status) {
      case 'available': return colors.success;
      case 'maintenance': return colors.warning;
      case 'cleaning': return colors.info;
      default: return colors.onSurfaceVariant;
    }
  };

  const getStatusText = (status: RoomStatus, isAvailable: boolean) => {
    return formatRoomStatus(status, isAvailable);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Search and Filters */}
      <View style={styles.filtersContainer}>
        <Searchbar
          placeholder="Search rooms..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersRow}>
          {/* Room Type Filter */}
          <View style={styles.filterGroup}>
            {roomTypes.map((type) => (
              <Chip
                key={type}
                selected={selectedType === type}
                onPress={() => setSelectedType(type)}
                style={[
                  styles.filterChip,
                  selectedType === type && styles.selectedFilterChip
                ]}
                textStyle={[
                  styles.filterChipText,
                  selectedType === type && styles.selectedFilterChipText
                ]}
              >
                {type === 'all' ? 'All Types' : formatRoomType(type)}
              </Chip>
            ))}
          </View>
        </ScrollView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersRow}>
          {/* Room Status Filter */}
          <View style={styles.filterGroup}>
            {roomStatuses.map((status) => (
              <Chip
                key={status}
                selected={selectedStatus === status}
                onPress={() => setSelectedStatus(status)}
                style={[
                  styles.filterChip,
                  selectedStatus === status && styles.selectedFilterChip
                ]}
                textStyle={[
                  styles.filterChipText,
                  selectedStatus === status && styles.selectedFilterChipText
                ]}
              >
                {status === 'all' ? 'All Status' : status.charAt(0).toUpperCase() + status.slice(1)}
              </Chip>
            ))}
          </View>
        </ScrollView>

        {/* Sort Menu */}
        <View style={styles.sortContainer}>
          <Menu
            visible={sortMenuVisible}
            onDismiss={() => setSortMenuVisible(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setSortMenuVisible(true)}
                icon="sort"
                compact
              >
                Sort by {sortBy}
              </Button>
            }
          >
            <Menu.Item
              onPress={() => {
                setSortBy('number');
                setSortMenuVisible(false);
              }}
              title="Room Number"
            />
            <Menu.Item
              onPress={() => {
                setSortBy('type');
                setSortMenuVisible(false);
              }}
              title="Room Type"
            />
            <Menu.Item
              onPress={() => {
                setSortBy('price');
                setSortMenuVisible(false);
              }}
              title="Price"
            />
          </Menu>
        </View>
      </View>

      {/* Results Header */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsText}>
          {filteredAndSortedRooms.length} room{filteredAndSortedRooms.length !== 1 ? 's' : ''} found
        </Text>
      </View>

      {/* Rooms List */}
      <ScrollView
        style={styles.roomsList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {filteredAndSortedRooms.map((room) => (
          <Card key={room.id} style={styles.roomCard}>
            <Card.Content>
              <View style={styles.roomHeader}>
                <View style={styles.roomInfo}>
                  <PaperText variant="titleMedium" style={styles.roomNumber}>Room {room.room_number}</PaperText>
                  <Text style={styles.roomType}>
                    {formatRoomType(room.type || room.room_type)}
                  </Text>
                  <Text style={styles.roomPrice}>{formatPrice(room.price_per_night)}/night</Text>
                </View>
                <Chip
                  style={[
                    styles.statusChip,
                    { backgroundColor: getStatusColor(room.status, room.is_available) }
                  ]}
                  textStyle={{ color: colors.surface }}
                >
                  {getStatusText(room.status, room.is_available)}
                </Chip>
              </View>

              <View style={styles.roomDetails}>
                <View style={styles.amenityInfo}>
                  <MaterialIcons name="people" size={16} color={colors.onSurfaceVariant} />
                  <Text style={styles.amenityText}>Max {room.max_occupancy} guests</Text>
                </View>
                <View style={styles.amenityInfo}>
                  <MaterialIcons name="bed" size={16} color={colors.onSurfaceVariant} />
                  <Text style={styles.amenityText}>{room.bed_type}</Text>
                </View>
              </View>

              <View style={styles.roomActions}>
                <Button
                  mode="outlined"
                  onPress={() => handleRoomPress(room)}
                  icon="pencil"
                  compact
                >
                  Edit
                </Button>
                
                <Button
                  mode="contained"
                  onPress={() => {
                    Alert.alert(
                      'Update Status',
                      'Choose new room status:',
                      [
                        { text: 'Available', onPress: () => handleStatusChange(room, 'available') },
                        { text: 'Maintenance', onPress: () => handleStatusChange(room, 'maintenance') },
                        { text: 'Cleaning', onPress: () => handleStatusChange(room, 'cleaning') },
                        { text: 'Cancel', style: 'cancel' },
                      ]
                    );
                  }}
                  icon="update"
                  compact
                >
                  Status
                </Button>
                
                <Button
                  mode="text"
                  onPress={() => confirmDeleteRoom(room)}
                  icon="delete"
                  textColor={colors.error}
                  compact
                >
                  Delete
                </Button>
              </View>
            </Card.Content>
          </Card>
        ))}

        {filteredAndSortedRooms.length === 0 && !loading && (
          <View style={styles.emptyState}>
            <MaterialIcons name="hotel" size={64} color={colors.onSurfaceVariant} />
            <Text style={styles.emptyStateText}>No rooms found</Text>
            <Text style={styles.emptyStateSubtext}>
              Try adjusting your search or filters
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Add Room FAB */}
      <FAB
        style={styles.fab}
        icon="plus"
        label="Add Room"
        onPress={() => navigation.navigate('AddEditRoom')}
      />

      {/* Delete Confirmation Dialog */}
      <Portal>
        <Dialog visible={deleteDialogVisible} onDismiss={() => setDeleteDialogVisible(false)}>
          <Dialog.Title>Delete Room</Dialog.Title>
          <Dialog.Content>
            <PaperText>
              Are you sure you want to delete Room {roomToDelete?.room_number}? 
              This action cannot be undone.
            </PaperText>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDeleteDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleDeleteRoom} textColor={colors.error}>Delete</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  filtersContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
  },
  filtersRow: {
    marginBottom: spacing.sm,
  },
  filterGroup: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  filterChip: {
    marginRight: spacing.sm,
    backgroundColor: colors.surfaceVariant,
    borderWidth: 1,
    borderColor: colors.outline || colors.border,
  },
  selectedFilterChip: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterChipText: {
    color: colors.onSurfaceVariant,
    fontSize: typography.sizes.sm,
  },
  selectedFilterChipText: {
    color: colors.onPrimary || colors.surface,
    fontWeight: typography.weights.medium as any,
  },
  sortContainer: {
    alignItems: 'flex-end',
    marginTop: spacing.sm,
  },
  resultsHeader: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
  },
  resultsText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
  },
  roomsList: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  roomCard: {
    marginBottom: spacing.md,
  },
  roomHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  roomInfo: {
    flex: 1,
  },
  roomNumber: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    marginBottom: spacing.xs,
  },
  roomType: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  roomPrice: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.primary,
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  roomDetails: {
    flexDirection: 'row',
    gap: spacing.lg,
    marginBottom: spacing.md,
  },
  amenityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  amenityText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
  },
  roomActions: {
    flexDirection: 'row',
    gap: spacing.sm,
    justifyContent: 'flex-end',
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: spacing.xl,
    backgroundColor: colors.primary,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
  },
  emptyStateSubtext: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    marginTop: spacing.xs,
  },
});
